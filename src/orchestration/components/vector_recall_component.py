"""基于向量的 Sheet 召回组件。"""
from __future__ import annotations

from typing import Dict, Any, List

from ..base_component import BaseComponent, register_component

from src.vectorization.vectorizer import Vectorizer
from src.vectorization.vector_store import ChromaVectorStore

import re  # 新增: 正则库用于提取中文

_CHINESE_RE = re.compile(r"[\u4e00-\u9fa5]+")


def _only_chinese(text: str) -> str:
    """仅保留中文汉字，用于字符串匹配。"""
    return "".join(_CHINESE_RE.findall(text))


def _longest_common_substr_len(a: str, b: str) -> int:
    """计算 a 与 b 的最长公共子串长度，简单 O(n^2) 算法，n≤30 影响可忽略。"""
    max_len = 0
    la, lb = len(a), len(b)
    for i in range(la):
        for j in range(i + 1, la + 1):
            sub = a[i:j]
            if len(sub) <= max_len:
                continue
            if sub in b:
                max_len = len(sub)
    return max_len


@register_component
class VectorRecallComponent(BaseComponent):  # noqa: D101
    name = "vector_recall"
    inputs = ["query"]
    outputs = ["query_vector", "sheet_ids", "retrieved_ids", "file_texts", "sheet_meta", "row_texts"]

    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        # 允许通过配置指定 embedding 模型（来自 qa_pipeline.yaml）
        embed_model = config.get("embedding_model")
        self.vectorizer = Vectorizer(model=embed_model) if embed_model else Vectorizer()
        # 不同层级向量写入到独立 collection，需分别连接
        self.store_file = ChromaVectorStore(collection_name="file_vectors")
        self.store_sheet = ChromaVectorStore(collection_name="sheet_vectors")
        self.store_row = ChromaVectorStore(collection_name="row_vectors")
        self.topk: int = config.get("topk", 3)
        # 最低层级概率阈值，低于该值的层级将跳过向量检索，可由 intent_classifier 透传到 strategy_params
        self.min_prob: float = config.get("min_prob", 0.3)
        # 新增: Sheet 级中文子串最小匹配长度阈值
        self.min_substr_len: int = config.get("min_substr_len", 3)
        import logging
        self.logger = logging.getLogger(self.name)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        question: str = payload.get("query", "")
        import logging
        if not hasattr(self, "logger"):
            self.logger = logging.getLogger(self.name)
        if not question:
            return {}

        # ---- 动态读取召回级别 & topk -----------------------------------
        strategy_params: Dict[str, Any] = payload.get("strategy_params", {})
        level: str = strategy_params.get("level", "sheet")
        topk: int = strategy_params.get("top_k", self.topk)

        self.logger.debug("Vector recall start | level=%s topk=%s question=%s", level, topk, question)

        # ---- 新增: 多层级召回 --------------------------------------
        level_probs: Dict[str, float] = payload.get("level_probs", {})  # type: ignore[assignment]
        if not level_probs:
            # 若 intent 未提供概率，则回退默认分布
            level_probs = {"file": 0.2, "sheet": 0.5, "row": 0.3}

        # 若 strategy_params 指定 level，则将该 level 概率拉高至 0.7，其余均匀分配剩余
        level_override = strategy_params.get("level")
        if level_override and level_override in ("file", "sheet", "row"):
            remain = 0.3
            other_levels = [l for l in ("file", "sheet", "row") if l != level_override]
            share = remain / len(other_levels)
            level_probs = {l: (0.7 if l == level_override else share) for l in ("file", "sheet", "row")}

        self.logger.debug("Level probabilities: %s", level_probs)

        # 允许通过 intent_classifier -> strategy_params 动态调整阈值
        min_prob_thr: float = strategy_params.get("min_prob", self.min_prob)

        vec: List[float] = self.vectorizer.embed_texts([question])[0]

        # -------------------------------------------------------------
        # 分层检索并加权排序
        # -------------------------------------------------------------
        candidates: Dict[str, float] = {}
        per_level_topk = topk  # 每层单独取 topk
        store_map = {
            "file": self.store_file,
            "sheet": self.store_sheet,
            "row": self.store_row,
        }
        for lvl in ("file", "sheet", "row"):
            prob_lvl = level_probs.get(lvl, 0.0)
            # 当该层级的意图概率低于 min_prob_thr 时，直接跳过检索
            if prob_lvl < min_prob_thr:
                # 概率低于阈值，跳过该层级检索
                self.logger.debug("Skip level %s due to low probability %.2f (< %.2f)", lvl, prob_lvl, min_prob_thr)
                continue
            try:
                store = store_map[lvl]
                ids_lvl = store.query(vec, top_k=per_level_topk, filter={"level": lvl})
            except Exception as exc:  # noqa: BLE001
                self.logger.warning("VectorStore query failed for level %s: %s", lvl, exc)
                ids_lvl = []
            weight = prob_lvl
            for rank, rid in enumerate(ids_lvl):
                # 采用简单线性衰减分数：score_raw = (per_level_topk - rank)/per_level_topk
                score_raw = (per_level_topk - rank) / per_level_topk
                score = score_raw * weight
                # 保留最高得分
                if rid not in candidates or candidates[rid] < score:
                    candidates[rid] = score

        if not candidates:
            self.logger.warning("Vector recall found no candidates, return empty")
            return {}

        # 排序并截断 topk
        sorted_ids = sorted(candidates.items(), key=lambda x: x[1], reverse=True)
        final_ids = [rid for rid, _ in sorted_ids[:topk]]

        # -------------------------------------------------------------
        # 取回 metadatas → 直接生成文本上下文，减少 DB 依赖
        # -------------------------------------------------------------
        file_texts: List[Dict[str, Any]] = []
        sheet_meta: List[Dict[str, Any]] = []
        row_texts: List[Dict[str, Any]] = []

        # 根据 id 前缀 / 约定决定从哪个 collection 读 metadata
        meta_file_ids = [rid for rid in final_ids if rid.startswith("file:")]
        meta_sheet_ids = [rid for rid in final_ids if rid.startswith("sheet:")]
        meta_row_ids = [rid for rid in final_ids if rid and ":" in rid and rid.split(":")[0].isdigit()]

        meta_results: List[tuple[str, Dict]] = []
        try:
            if meta_file_ids:
                ret = self.store_file.collection.get(ids=meta_file_ids, include=["metadatas"])  # type: ignore[attr-defined]
                ids_val = ret.get("ids") or []
                metas_val = ret.get("metadatas") or []
                meta_results.extend(list(zip(ids_val, metas_val)))  # type: ignore[arg-type]
            if meta_sheet_ids:
                ret = self.store_sheet.collection.get(ids=meta_sheet_ids, include=["metadatas"])  # type: ignore[attr-defined]
                ids_val = ret.get("ids") or []
                metas_val = ret.get("metadatas") or []
                meta_results.extend(list(zip(ids_val, metas_val)))  # type: ignore[arg-type]
            if meta_row_ids:
                ret = self.store_row.collection.get(ids=meta_row_ids, include=["metadatas"])  # type: ignore[attr-defined]
                ids_val = ret.get("ids") or []
                metas_val = ret.get("metadatas") or []
                meta_results.extend(list(zip(ids_val, metas_val)))  # type: ignore[arg-type]
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("Fetch metadata failed: %s", _ex)

        for rid, meta in meta_results:
            if not meta:
                continue
            level = str(meta.get("level"))
            if level == "file":
                file_texts.append({
                    "file_id": meta.get("file_id"),
                    "key": meta.get("key"),
                    "text": meta.get("text", ""),
                })
            elif level == "sheet":
                # ---------------- Sheet 级字符串匹配过滤 ----------------
                pure_query = _only_chinese(question)
                embed_text = str(meta.get("text", meta.get("description", "")))
                pure_embed = _only_chinese(embed_text)[:30]
                if _longest_common_substr_len(pure_query, pure_embed) < self.min_substr_len:
                    # 不满足匹配要求，跳过该 sheet
                    self.logger.debug("过滤 Sheet %s，最长公共子串不足 %d", rid, self.min_substr_len)
                    continue
                sheet_meta.append({
                    "sheet_id": meta.get("sheet_id"),
                    "sheet_name": meta.get("sheet_name"),
                    "description": meta.get("text", meta.get("description")),
                })
            elif level == "row":
                row_texts.append({
                    "sheet_id": meta.get("sheet_id"),
                    "rowid": meta.get("rowid"),
                    "text": meta.get("text", ""),
                })

        # -------------------------------------------------------------
        # 生成 sheet_ids（供后续组件使用）
        # -------------------------------------------------------------
        sheet_ids_set: set[str] = set()
        allowed_sheet_ids: set[str] = {f"sheet:{m['sheet_id']}" for m in sheet_meta}
        for rid in final_ids:
            if rid.startswith("sheet:"):
                if rid in allowed_sheet_ids:
                    sheet_ids_set.add(rid)
            else:
                # 尝试解析 row 级 id -> 取 sheet_id 部分
                parts = rid.split(":")
                if len(parts) >= 2 and parts[0].isdigit():
                    sheet_ids_set.add(f"sheet:{parts[0]}")
        sheet_ids = list(sheet_ids_set)

        # 使用过滤后的 sheet_ids 更新 retrieved_ids
        final_ids = [rid for rid in final_ids if not rid.startswith("sheet:") or rid in allowed_sheet_ids]
        # -------------------------------------------------------------

        self.logger.info(
            "Vector recall finished | candidates=%s final=%s sheet_ids=%s",
            len(candidates),
            len(final_ids),
            len(sheet_ids),
        )
        self.logger.debug("Top ids: %s", final_ids)

        out: Dict[str, Any] = {
            "query_vector": vec,
            "sheet_ids": sheet_ids,
            "retrieved_ids": final_ids,
        }
        if file_texts:
            out["file_texts"] = file_texts
        if sheet_meta:
            out["sheet_meta"] = sheet_meta
        if row_texts:
            # 同时保留原始向量召回结果，供控制台展示
            out["row_texts_raw"] = list(row_texts)  # shallow copy
            out["row_texts"] = row_texts
        return out 