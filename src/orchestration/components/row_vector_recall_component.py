from __future__ import annotations
from typing import Dict, Any, List
import logging

from ..base_component import BaseComponent, register_component
from src.vectorization.vectorizer import embed_texts
from src.vectorization.vector_store import ChromaVectorStore


"""RowVectorRecallComponent：在 intent == 'row' 场景下，一次性完成行级向量召回、
   sheet 相关性评估以及行重排。

   1. 计算 query embedding → row_vectors & sheet_vectors 分别召回。
   2. 取 top-k 行向量 (含 text 元数据)，无需再访问 SQLite。
   3. 找到最相关 sheet_id，将属于该 sheet 的行排在最前，再按距离升序。
   4. 最终返回前 top_n 行文本供下游 (rerank / llm_answer) 直接使用。
"""


@register_component
class RowVectorRecallComponent(BaseComponent):  # noqa: D101
    name = "row_vector_recall"
    inputs = ["query", "intent"]
    outputs = ["row_texts", "sheet_ids", "level_probs"]

    kw_weight: float

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        self.top_k_rows: int = int(config.get("top_k_rows", 20))
        self.top_n_rows: int = int(config.get("top_n_rows", 5))
        # collection 名称可通过配置覆盖
        self.row_collection: str = str(config.get("row_collection", "row_vectors"))
        self.sheet_collection: str = str(config.get("sheet_collection", "sheet_vectors"))
        self.logger = logging.getLogger(self.name)

        # 关键词权重系数 (0-1)
        self.kw_weight = float(config.get("kw_weight", 0.3))

        # 初始化 VectorStore（不在 run() 内反复构造，节约开销）
        self._row_vs = ChromaVectorStore(collection_name=self.row_collection)
        self._sheet_vs = ChromaVectorStore(collection_name=self.sheet_collection)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        # 仅当 intent == 'row' 时执行，其他场景直接跳过，不干扰现有流程。
        if payload.get("intent") != "row":
            return {}

        question: str = payload.get("query", "")
        if not question:
            self.logger.warning("query 为空，RowVectorRecallComponent 跳过执行")
            return {}

        # 1) embed query
        embedding = embed_texts([question])[0]
        if not embedding:
            self.logger.error("生成 query embedding 失败，RowVectorRecallComponent 中止")
            return {}

        # 2) row 向量召回 (包含 metadatas) - 增加表名匹配的预筛选
        # 首先尝试召回更多结果，然后进行表名匹配筛选
        initial_k = min(self.top_k_rows * 10, 200)  # 召回更多结果用于表名匹配
        row_ret = self._row_vs.collection.query(  # type: ignore[attr-defined]
            query_embeddings=[embedding],
            n_results=initial_k,
            include=["metadatas", "distances"],
        )
        # -------- 调试日志：原始 row 召回结果 -----------------------------
        try:
            _ids_log = row_ret.get("ids", [[]])[0][:5]  # type: ignore[index]
            _dists_log = row_ret.get("distances", [[]])[0][:5]  # type: ignore[index]
            self.logger.debug("Row recall raw | ids=%s distances=%s", _ids_log, _dists_log)
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("log row recall raw failed: %s", _ex)

        if not row_ret.get("ids"):
            self.logger.info("未召回到任何行向量")
            return {}

        from typing import cast

        row_ids: List[str] = cast(List[str], row_ret["ids"][0])  # type: ignore[index]
        row_metas: List[Dict[str, Any]] = cast(List[Dict[str, Any]], row_ret["metadatas"][0])  # type: ignore[index]
        row_dists: List[float] = cast(List[float], row_ret["distances"][0])  # type: ignore[index]

        # 提取表名关键词（需要在这里先定义）
        def _extract_table_keywords(query: str) -> list[str]:
            """从查询中提取可能的表名关键词"""
            # 常见的表名模式
            table_patterns = [
                r"(\w*表)",  # 以"表"结尾的词
                r"(\w*信息表)",  # 以"信息表"结尾的词
                r"(\w*业务\w*表)",  # 包含"业务"的表名
                r"(\w*余额表)",  # 以"余额表"结尾的词
                r"(\w*明细表)",  # 以"明细表"结尾的词
            ]

            keywords = []
            for pattern in table_patterns:
                matches = re.findall(pattern, query)
                keywords.extend(matches)

            # 去重并过滤掉过短的关键词
            keywords = list(set([kw for kw in keywords if len(kw) >= 3]))
            return keywords

        table_keywords = _extract_table_keywords(question)
        self.logger.debug("Extracted table keywords from query: %s", table_keywords)
        print(f"[DEBUG] Extracted table keywords from query: {table_keywords}")  # 临时调试

        # 表名匹配预筛选：优先选择包含目标表名的行
        table_matched_rows = []
        other_rows = []

        for rid, meta, dist in zip(row_ids, row_metas, row_dists):
            text = meta.get("text", "")
            has_table_match = False

            # 检查是否包含用户查询的表名关键词
            if table_keywords:
                for keyword in table_keywords:
                    if keyword in text:
                        has_table_match = True
                        break

            row_data = (rid, meta, dist)
            if has_table_match:
                table_matched_rows.append(row_data)
            else:
                other_rows.append(row_data)

        # 优先使用表名匹配的行，不足时补充其他行
        selected_rows = table_matched_rows[:self.top_k_rows]
        if len(selected_rows) < self.top_k_rows:
            remaining = self.top_k_rows - len(selected_rows)
            selected_rows.extend(other_rows[:remaining])

        self.logger.debug("Table matching pre-filter: %d matched, %d others, %d selected",
                         len(table_matched_rows), len(other_rows), len(selected_rows))

        rows: List[Dict[str, Any]] = []
        for rid, meta, dist in selected_rows:
            # metadata 至少需包含 sheet_id, text
            rows.append(
                {
                    "id": rid,
                    "sheet_id": meta.get("sheet_id"),
                    "rowid": meta.get("rowid"),
                    "text": meta.get("text", ""),
                    "dist": float(dist),
                }
            )

        # ----------------------- 关键词相似度 (v2: difflib + 表名匹配增强) ----------------
        from difflib import SequenceMatcher
        import re

        # 预处理查询文本，移除常见分隔符
        q_norm = re.sub(r"[\s,:|]", "", question)

        # 使用前面已经提取的table_keywords

        def _kw_sim(text: str) -> float:  # noqa: D401
            """使用 difflib 计算查询与文本开头的相似度，并增强表名匹配。"""
            if not text:
                return 0.0

            # 基础相似度计算
            text_prefix = text[: len(q_norm) + 20]  # 加一些 buffer
            text_prefix_norm = re.sub(r"[\s,:|]", "", text_prefix)

            if not text_prefix_norm:
                return 0.0

            # SequenceMatcher.ratio() is a good measure of similarity
            matcher = SequenceMatcher(a=q_norm, b=text_prefix_norm, autojunk=False)
            base_score = matcher.ratio()

            # 表名匹配增强：如果text中包含用户查询的表名关键词，给予额外加分
            table_boost = 0.0
            if table_keywords:
                for keyword in table_keywords:
                    if keyword in text:
                        # 根据关键词长度给予不同的加分，长关键词加分更多
                        boost = min(0.3, len(keyword) * 0.05)  # 最多加0.3分
                        table_boost = max(table_boost, boost)
                        self.logger.debug("Table keyword '%s' found in text, boost: %.3f", keyword, boost)
                        print(f"[DEBUG] Table keyword '{keyword}' found in text, boost: {boost:.3f}")  # 临时调试

            final_score = min(1.0, base_score + table_boost)  # 确保不超过1.0
            return final_score

        for r in rows:
            vec_score = 1.0 / (1.0 + r["dist"]) if r["dist"] is not None else 0.0
            kw_score = _kw_sim(r["text"])
            r["_score"] = (1 - self.kw_weight) * vec_score + self.kw_weight * kw_score
 
        # -------- 调试日志: 关键词 & 综合得分 -----------------------------
        try:
            self.logger.debug(
                "Normalized query for KW sim: %s", q_norm,
            )
            self.logger.debug(
                "Rows kw/vec/combined top10: %s",
                [
                    {
                        "rowid": r["rowid"],
                        "sheet": r["sheet_id"],
                        "vec_score": round(1 / (1 + r["dist"]), 4),
                        "kw": round(_kw_sim(r["text"]), 4),
                        "score": round(r["_score"], 4),
                    }
                    for r in rows[:10]
                ],
            )
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("log kw score failed: %s", _ex)

        # 3) sheet 向量召回，取最相关 sheet_id
        sheet_ret = self._sheet_vs.collection.query(  # type: ignore[attr-defined]
            query_embeddings=[embedding],
            n_results=1,
            include=["metadatas"],
        )

        top_sheet_id = None
        metas = sheet_ret.get("metadatas")  # type: ignore[index]
        # metas 结构: List[List[Dict]]，还需确保两层列表均非空
        if metas and metas[0]:
            top_sheet_id = metas[0][0].get("sheet_id")  # type: ignore[index]
            self.logger.debug("Top sheet_id via vector: %s", top_sheet_id)

        # 4) 行重排：优先同 sheet，其次按综合得分 (_score 越高越优)
        if top_sheet_id is not None:
            rows.sort(key=lambda r: (r["sheet_id"] != top_sheet_id, -r["_score"]))
        else:
            rows.sort(key=lambda r: -r["_score"])

        # -------- 调试日志：行重排后的前 10 条 -----------------------------
        try:
            self.logger.debug(
                "Rows after rerank (top10): %s",
                [
                    {
                        "sheet_id": r["sheet_id"],
                        "rowid": r["rowid"],
                        "dist": round(r["dist"], 4),
                    }
                    for r in rows[:10]
                ],
            )
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("log rows after rerank failed: %s", _ex)

        # 5) 组装返回 top_n 行文本
        selected_rows = rows[: self.top_n_rows]
        row_texts = [
            {"sheet_id": r["sheet_id"], "rowid": r["rowid"], "text": r["text"]}
            for r in selected_rows
            if r["text"]
        ]

        # -------- 调试日志：最终返回的行文本 -----------------------------
        try:
            # Log the complete row text without truncation for easier inspection.
            row_preview = [r.get("text", "") for r in row_texts]
            self.logger.debug(
                "Selected row_texts (top_n=%s): %s",
                self.top_n_rows,
                row_preview,
            )
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("log selected row_texts failed: %s", _ex)

        # level 概率分布保持与旧逻辑一致，避免下游修改
        level_probs = {"file": 0.1, "sheet": 0.2, "row": 0.7}
        sheet_ids = [f"sheet:{top_sheet_id}"] if top_sheet_id is not None else []

        self.logger.info(
            "Row recall finished | rows=%s top_sheet=%s", len(row_texts), top_sheet_id
        )
        return {
            "row_texts": row_texts,
            "sheet_ids": sheet_ids,
            "level_probs": level_probs,
        } 