from __future__ import annotations
from typing import Dict, Any, List
import logging

from ..base_component import BaseComponent, register_component
from src.vectorization.vectorizer import embed_texts
from src.vectorization.vector_store import ChromaVectorStore


"""RowVectorRecallComponent：在 intent == 'row' 场景下，一次性完成行级向量召回、
   sheet 相关性评估以及行重排。

   1. 计算 query embedding → row_vectors & sheet_vectors 分别召回。
   2. 取 top-k 行向量 (含 text 元数据)，无需再访问 SQLite。
   3. 找到最相关 sheet_id，将属于该 sheet 的行排在最前，再按距离升序。
   4. 最终返回前 top_n 行文本供下游 (rerank / llm_answer) 直接使用。
"""


@register_component
class RowVectorRecallComponent(BaseComponent):  # noqa: D101
    name = "row_vector_recall"
    inputs = ["query", "intent"]
    outputs = ["row_texts", "sheet_ids", "level_probs"]

    kw_weight: float

    # ------------------------------------------------------------------
    def setup(self, config: Dict[str, Any]) -> None:  # noqa: D401
        self.top_k_rows: int = int(config.get("top_k_rows", 20))
        self.top_n_rows: int = int(config.get("top_n_rows", 5))
        # collection 名称可通过配置覆盖
        self.row_collection: str = str(config.get("row_collection", "row_vectors"))
        self.sheet_collection: str = str(config.get("sheet_collection", "sheet_vectors"))
        self.logger = logging.getLogger(self.name)

        # 关键词权重系数 (0-1)
        self.kw_weight = float(config.get("kw_weight", 0.3))

        # 初始化 VectorStore（不在 run() 内反复构造，节约开销）
        self._row_vs = ChromaVectorStore(collection_name=self.row_collection)
        self._sheet_vs = ChromaVectorStore(collection_name=self.sheet_collection)

    # ------------------------------------------------------------------
    def run(self, payload: Dict[str, Any], **kwargs: Any) -> Dict[str, Any]:  # noqa: D401
        # 仅当 intent == 'row' 时执行，其他场景直接跳过，不干扰现有流程。
        if payload.get("intent") != "row":
            return {}

        question: str = payload.get("query", "")
        if not question:
            self.logger.warning("query 为空，RowVectorRecallComponent 跳过执行")
            return {}

        # 1) embed query
        embedding = embed_texts([question])[0]
        if not embedding:
            self.logger.error("生成 query embedding 失败，RowVectorRecallComponent 中止")
            return {}

        # 2) row 向量召回 (包含 metadatas)
        # Chroma v0.5+ 自动返回 ids，include 参数中无需显式指定
        row_ret = self._row_vs.collection.query(  # type: ignore[attr-defined]
            query_embeddings=[embedding],
            n_results=self.top_k_rows,
            include=["metadatas", "distances"],
        )
        # -------- 调试日志：原始 row 召回结果 -----------------------------
        try:
            _ids_log = row_ret.get("ids", [[]])[0][:5]  # type: ignore[index]
            _dists_log = row_ret.get("distances", [[]])[0][:5]  # type: ignore[index]
            self.logger.debug("Row recall raw | ids=%s distances=%s", _ids_log, _dists_log)
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("log row recall raw failed: %s", _ex)

        if not row_ret.get("ids"):
            self.logger.info("未召回到任何行向量")
            return {}

        from typing import cast

        row_ids: List[str] = cast(List[str], row_ret["ids"][0])  # type: ignore[index]
        row_metas: List[Dict[str, Any]] = cast(List[Dict[str, Any]], row_ret["metadatas"][0])  # type: ignore[index]
        row_dists: List[float] = cast(List[float], row_ret["distances"][0])  # type: ignore[index]

        rows: List[Dict[str, Any]] = []
        for rid, meta, dist in zip(row_ids, row_metas, row_dists):
            # metadata 至少需包含 sheet_id, text
            rows.append(
                {
                    "id": rid,
                    "sheet_id": meta.get("sheet_id"),
                    "rowid": meta.get("rowid"),
                    "text": meta.get("text", ""),
                    "dist": float(dist),
                }
            )

        # ----------------------- 关键词相似度 (v2: difflib) ----------------
        from difflib import SequenceMatcher
        import re

        # 预处理查询文本，移除常见分隔符
        q_norm = re.sub(r"[\s,:|]", "", question)

        def _kw_sim(text: str) -> float:  # noqa: D401
            """使用 difflib 计算查询与文本开头的相似度。"""
            if not text:
                return 0.0
            # 只取文本开头部分参与比较，长度与查询文本相近
            text_prefix = text[: len(q_norm) + 20]  # 加一些 buffer
            text_prefix_norm = re.sub(r"[\s,:|]", "", text_prefix)

            if not text_prefix_norm:
                return 0.0

            # SequenceMatcher.ratio() is a good measure of similarity
            matcher = SequenceMatcher(a=q_norm, b=text_prefix_norm, autojunk=False)
            return matcher.ratio()

        for r in rows:
            vec_score = 1.0 / (1.0 + r["dist"]) if r["dist"] is not None else 0.0
            kw_score = _kw_sim(r["text"])
            r["_score"] = (1 - self.kw_weight) * vec_score + self.kw_weight * kw_score
 
        # -------- 调试日志: 关键词 & 综合得分 -----------------------------
        try:
            self.logger.debug(
                "Normalized query for KW sim: %s", q_norm,
            )
            self.logger.debug(
                "Rows kw/vec/combined top10: %s",
                [
                    {
                        "rowid": r["rowid"],
                        "sheet": r["sheet_id"],
                        "vec_score": round(1 / (1 + r["dist"]), 4),
                        "kw": round(_kw_sim(r["text"]), 4),
                        "score": round(r["_score"], 4),
                    }
                    for r in rows[:10]
                ],
            )
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("log kw score failed: %s", _ex)

        # 3) sheet 向量召回，取最相关 sheet_id
        sheet_ret = self._sheet_vs.collection.query(  # type: ignore[attr-defined]
            query_embeddings=[embedding],
            n_results=1,
            include=["metadatas"],
        )

        top_sheet_id = None
        metas = sheet_ret.get("metadatas")  # type: ignore[index]
        # metas 结构: List[List[Dict]]，还需确保两层列表均非空
        if metas and metas[0]:
            top_sheet_id = metas[0][0].get("sheet_id")  # type: ignore[index]
            self.logger.debug("Top sheet_id via vector: %s", top_sheet_id)

        # 4) 行重排：优先同 sheet，其次按综合得分 (_score 越高越优)
        if top_sheet_id is not None:
            rows.sort(key=lambda r: (r["sheet_id"] != top_sheet_id, -r["_score"]))
        else:
            rows.sort(key=lambda r: -r["_score"])

        # -------- 调试日志：行重排后的前 10 条 -----------------------------
        try:
            self.logger.debug(
                "Rows after rerank (top10): %s",
                [
                    {
                        "sheet_id": r["sheet_id"],
                        "rowid": r["rowid"],
                        "dist": round(r["dist"], 4),
                    }
                    for r in rows[:10]
                ],
            )
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("log rows after rerank failed: %s", _ex)

        # 5) 组装返回 top_n 行文本
        selected_rows = rows[: self.top_n_rows]
        row_texts = [
            {"sheet_id": r["sheet_id"], "rowid": r["rowid"], "text": r["text"]}
            for r in selected_rows
            if r["text"]
        ]

        # -------- 调试日志：最终返回的行文本 -----------------------------
        try:
            # Log the complete row text without truncation for easier inspection.
            row_preview = [r.get("text", "") for r in row_texts]
            self.logger.debug(
                "Selected row_texts (top_n=%s): %s",
                self.top_n_rows,
                row_preview,
            )
        except Exception as _ex:  # noqa: BLE001
            self.logger.debug("log selected row_texts failed: %s", _ex)

        # level 概率分布保持与旧逻辑一致，避免下游修改
        level_probs = {"file": 0.1, "sheet": 0.2, "row": 0.7}
        sheet_ids = [f"sheet:{top_sheet_id}"] if top_sheet_id is not None else []

        self.logger.info(
            "Row recall finished | rows=%s top_sheet=%s", len(row_texts), top_sheet_id
        )
        return {
            "row_texts": row_texts,
            "sheet_ids": sheet_ids,
            "level_probs": level_probs,
        } 